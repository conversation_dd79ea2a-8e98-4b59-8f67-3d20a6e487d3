import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { SupabaseService } from '../../supabase/supabase.service';
import { User as PublicUserModel } from '../../../generated/prisma';
export interface SupabaseJwtPayload {
    sub: string;
    email?: string;
    phone?: string;
    role?: string;
    aud?: string;
    exp?: number;
    iat?: number;
    app_metadata?: {
        [key: string]: any;
    };
    user_metadata?: {
        [key: string]: any;
    };
}
export interface ReqUserObject {
    authData: any;
    dbProfile: PublicUserModel | null;
    profileExistsInDb: boolean;
}
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithoutRequest] | [opt: import("passport-jwt").StrategyOptionsWithRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private readonly configService;
    private readonly prismaService;
    private readonly supabaseService;
    constructor(configService: ConfigService, prismaService: PrismaService, supabaseService: SupabaseService);
    validate(req: any, payload: SupabaseJwtPayload): Promise<ReqUserObject>;
}
export {};
